/* Base Styling */
:root {
    --primary-color: #0275d8; /* Blue */
    --secondary-color: #000000; /* Black */
    --accent-color: #0275d8; /* Blue accent */
    --light-color: #ffffff; /* White */
    --dark-color: #000000; /* Black */
    --success-color: #0275d8; /* Blue success */
    --text-color: #333333; /* Dark text */
    --light-gray: #f8f9fa; /* Light gray for backgrounds */
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* Navigation */
.navbar {
    background-color: var(--dark-color);
    padding: 1rem 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: #fff;
}

.nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: #fff !important;
    transform: translateY(-2px);
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 2rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-title {
    color: var(--secondary-color);
    font-weight: 600;
}

.card-text {
    color: #666;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.5rem 1.5rem;
    border-radius: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Forms */
.form-control {
    border-radius: 5px;
    padding: 0.7rem 1rem;
    border: 1px solid #ddd;
    margin-bottom: 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 2rem 0;
}

footer h5 {
    color: #fff;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

footer a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #fff;
    text-decoration: none;
}

.social-icons a {
    display: inline-block;
    margin-right: 15px;
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    color: #fff;
    transform: translateY(-3px);
}

/* Video Player */
video {
    border-radius: 5px;
    margin-top: 1rem;
    width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Homepage */
.hero-section {
    padding: 4rem 0;
    background: linear-gradient(to right, var(--primary-color), #2980b9);
    color: white;
    text-align: center;
    margin-bottom: 3rem;
    border-radius: 10px;
}

.hero-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    max-width: 800px;
    margin: 0 auto 2rem;
}

/* Blog and Updates Styling */
.blog-post {
    margin-bottom: 2.5rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 2rem;
}

.blog-post:last-child {
    border-bottom: none;
}

.blog-post-title {
    color: var(--secondary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.blog-post-meta {
    color: #6c757d;
    margin-bottom: 1.25rem;
    font-size: 0.9rem;
}

.blog-post-content {
    line-height: 1.8;
}

.blog-sidebar {
    position: sticky;
    top: 20px;
}

.blog-sidebar-section {
    margin-bottom: 2rem;
    background-color: var(--light-color);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.blog-sidebar-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.updates-section {
    background-color: var(--light-color);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.updates-header {
    color: var(--secondary-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    display: inline-block;
}

.update-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.update-item:last-child {
    border-bottom: none;
}

.update-date {
    font-size: 0.85rem;
    color: var(--primary-color);
    font-weight: 600;
}

.update-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0.5rem 0;
}

.update-content {
    color: #555;
}

.tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.3rem 0.7rem;
    border-radius: 20px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.tag:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .col-md-4 {
        margin-bottom: 20px;
    }
    
    .blog-sidebar {
        position: static;
        margin-top: 2rem;
    }
    
    .updates-section {
        padding: 1.5rem;
    }
}
