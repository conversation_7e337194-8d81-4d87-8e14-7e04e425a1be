/* Modern Theme System */
:root {
    /* Light Theme Colors */
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #1f2937;
    --accent-color: #10b981;
    --accent-hover: #059669;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-card: #ffffff;

    /* Text Colors */
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --border-dark: #9ca3af;

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-card: #1f2937;

    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --text-inverse: #111827;

    --border-light: #374151;
    --border-medium: #4b5563;
    --border-dark: #6b7280;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Base Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    font-size: 16px;
    font-weight: 400;
}

/* Navigation */
.navbar {
    background-color: var(--bg-card);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-md) 0;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: color var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--primary-color);
}

.navbar-brand i {
    font-size: 1.75rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    margin: 0 var(--space-sm);
    padding: var(--space-sm) var(--space-md) !important;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--bg-secondary);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--bg-secondary);
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-md);
    padding: var(--space-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-left: var(--space-md);
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.05);
}

.theme-toggle i {
    font-size: 1.1rem;
}

/* Cards */
.card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    margin-bottom: var(--space-xl);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-medium);
}

.card:hover::before {
    opacity: 1;
}

.card-body {
    padding: var(--space-xl);
}

.card-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: var(--space-md);
    line-height: 1.3;
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.card-meta {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: var(--space-md);
}

/* Modern Buttons */
.btn {
    font-weight: 600;
    border-radius: var(--radius-md);
    padding: var(--space-sm) var(--space-lg);
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-inverse);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-light {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.btn-light:hover {
    background-color: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-primary);
}

/* Modern Forms */
.form-control {
    background-color: var(--bg-card);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--space-md) var(--space-lg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-fast);
    margin-bottom: var(--space-lg);
    width: 100%;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background-color: var(--bg-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    display: block;
}

.form-group {
    margin-bottom: var(--space-xl);
}

/* Footer */
footer {
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 2rem 0;
}

footer h5 {
    color: #fff;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

footer a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #fff;
    text-decoration: none;
}

.social-icons a {
    display: inline-block;
    margin-right: 15px;
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    color: #fff;
    transform: translateY(-3px);
}

/* Video Player */
video {
    border-radius: 5px;
    margin-top: 1rem;
    width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Modern Hero Section */
.hero-section {
    background: var(--gradient-hero);
    color: var(--text-inverse);
    text-align: center;
    padding: var(--space-2xl) 0;
    margin-bottom: var(--space-2xl);
    border-radius: var(--radius-2xl);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-section h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: var(--space-lg);
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-section p {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    max-width: 600px;
    margin: 0 auto var(--space-xl);
    opacity: 0.95;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-xl);
    margin-top: var(--space-xl);
    flex-wrap: wrap;
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    font-size: 2rem;
    font-weight: 700;
    display: block;
    margin-bottom: var(--space-xs);
}

.hero-stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Blog and Updates Styling */
.blog-post {
    margin-bottom: 2.5rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 2rem;
}

.blog-post:last-child {
    border-bottom: none;
}

.blog-post-title {
    color: var(--secondary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.blog-post-meta {
    color: #6c757d;
    margin-bottom: 1.25rem;
    font-size: 0.9rem;
}

.blog-post-content {
    line-height: 1.8;
}

.blog-sidebar {
    position: sticky;
    top: 20px;
}

.blog-sidebar-section {
    margin-bottom: 2rem;
    background-color: var(--light-color);
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.blog-sidebar-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.updates-section {
    background-color: var(--light-color);
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.updates-header {
    color: var(--secondary-color);
    font-weight: 700;
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    display: inline-block;
}

.update-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.update-item:last-child {
    border-bottom: none;
}

.update-date {
    font-size: 0.85rem;
    color: var(--primary-color);
    font-weight: 600;
}

.update-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin: 0.5rem 0;
}

.update-content {
    color: #555;
}

.tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.3rem 0.7rem;
    border-radius: 20px;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.tag:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Responsive */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .col-md-4 {
        margin-bottom: 20px;
    }
    
    .blog-sidebar {
        position: static;
        margin-top: 2rem;
    }
    
    .updates-section {
        padding: 1.5rem;
    }
}
