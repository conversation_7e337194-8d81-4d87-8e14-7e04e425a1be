/* Modern Theme System */
:root {
    /* Light Theme Colors */
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --secondary-color: #1f2937;
    --accent-color: #10b981;
    --accent-hover: #059669;

    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --bg-card: #ffffff;

    /* Text Colors */
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --text-inverse: #ffffff;

    /* Border Colors */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;
    --border-dark: #9ca3af;

    /* Shadow Colors */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-card: #1f2937;

    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --text-inverse: #111827;

    --border-light: #374151;
    --border-medium: #4b5563;
    --border-dark: #6b7280;

    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Base Styling */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-normal), color var(--transition-normal);
    font-size: 16px;
    font-weight: 400;
}

/* Navigation */
.navbar {
    background-color: var(--bg-card);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    padding: var(--space-md) 0;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--text-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    transition: color var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--primary-color);
}

.navbar-brand i {
    font-size: 1.75rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    margin: 0 var(--space-sm);
    padding: var(--space-sm) var(--space-md) !important;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background-color: var(--bg-secondary);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--bg-secondary);
}

/* Theme Toggle Button */
.theme-toggle {
    background: none;
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-md);
    padding: var(--space-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-left: var(--space-md);
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.05);
}

.theme-toggle i {
    font-size: 1.1rem;
}

/* Cards */
.card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    margin-bottom: var(--space-xl);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-medium);
}

.card:hover::before {
    opacity: 1;
}

.card-body {
    padding: var(--space-xl);
}

.card-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: var(--space-md);
    line-height: 1.3;
}

.card-text {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.card-meta {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin-bottom: var(--space-md);
}

/* Modern Buttons */
.btn {
    font-weight: 600;
    border-radius: var(--radius-md);
    padding: var(--space-sm) var(--space-lg);
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-inverse);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-light {
    background-color: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

.btn-light:hover {
    background-color: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-primary);
}

/* Modern Forms */
.form-control {
    background-color: var(--bg-card);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: var(--space-md) var(--space-lg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all var(--transition-fast);
    margin-bottom: var(--space-lg);
    width: 100%;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    background-color: var(--bg-primary);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-label {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--space-sm);
    display: block;
}

.form-group {
    margin-bottom: var(--space-xl);
}

/* Modern Footer */
footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
    padding: var(--space-2xl) 0 var(--space-xl);
    margin-top: var(--space-2xl);
}

footer h5 {
    color: var(--text-primary);
    margin-bottom: var(--space-lg);
    font-weight: 700;
    font-size: 1.125rem;
}

footer p {
    color: var(--text-secondary);
    line-height: 1.6;
}

footer a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
    display: block;
    padding: var(--space-xs) 0;
}

footer a:hover {
    color: var(--primary-color);
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: var(--space-sm);
}

.social-icons {
    display: flex;
    gap: var(--space-md);
    margin-top: var(--space-lg);
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 1.25rem;
    transition: all var(--transition-fast);
    padding: 0;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid var(--border-light);
    margin-top: var(--space-xl);
    padding-top: var(--space-lg);
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Modern Video Player */
video {
    border-radius: var(--radius-lg);
    margin-top: var(--space-lg);
    width: 100%;
    box-shadow: var(--shadow-lg);
    transition: transform var(--transition-normal);
}

video:hover {
    transform: scale(1.02);
}

/* Section Styling */
.section {
    margin-bottom: var(--space-2xl);
}

.section-title {
    font-size: clamp(1.75rem, 3vw, 2.25rem);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xl);
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Updates Section */
.updates-section {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    margin-bottom: var(--space-2xl);
    box-shadow: var(--shadow-md);
}

.update-item {
    padding: var(--space-lg) 0;
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.update-item:last-child {
    border-bottom: none;
}

.update-item:hover {
    background-color: var(--bg-secondary);
    margin: 0 calc(-1 * var(--space-lg));
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
    border-radius: var(--radius-md);
}

.update-date {
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.update-title {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
    font-size: 1.125rem;
}

.update-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Tags */
.tag {
    display: inline-block;
    background: var(--gradient-primary);
    color: var(--text-inverse);
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--space-xs) var(--space-md);
    border-radius: var(--radius-xl);
    margin-right: var(--space-sm);
    margin-bottom: var(--space-sm);
    transition: all var(--transition-fast);
}

.tag:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-light);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Modern Hero Section */
.hero-section {
    background: var(--gradient-hero);
    color: var(--text-inverse);
    text-align: center;
    padding: var(--space-2xl) 0;
    margin-bottom: var(--space-2xl);
    border-radius: var(--radius-2xl);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 1;
}

.hero-section h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: var(--space-lg);
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-section p {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    max-width: 600px;
    margin: 0 auto var(--space-xl);
    opacity: 0.95;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-xl);
    margin-top: var(--space-xl);
    flex-wrap: wrap;
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    font-size: 2rem;
    font-weight: 700;
    display: block;
    margin-bottom: var(--space-xs);
}

.hero-stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Modern Responsive Design */
@media (max-width: 1200px) {
    .container {
        padding-left: var(--space-lg);
        padding-right: var(--space-lg);
    }
}

@media (max-width: 768px) {
    :root {
        --space-xs: 0.25rem;
        --space-sm: 0.5rem;
        --space-md: 0.75rem;
        --space-lg: 1rem;
        --space-xl: 1.5rem;
        --space-2xl: 2rem;
    }

    .navbar {
        padding: var(--space-sm) 0;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }

    .nav-link {
        margin: var(--space-xs) 0;
        padding: var(--space-md) var(--space-lg) !important;
    }

    .hero-section {
        padding: var(--space-xl) var(--space-md);
        margin-bottom: var(--space-xl);
        border-radius: var(--radius-lg);
    }

    .hero-stats {
        gap: var(--space-lg);
    }

    .hero-stat-number {
        font-size: 1.5rem;
    }

    .card-body {
        padding: var(--space-lg);
    }

    .updates-section {
        padding: var(--space-lg);
    }

    .btn {
        padding: var(--space-md) var(--space-lg);
        font-size: 0.875rem;
    }

    .social-icons {
        justify-content: center;
        margin-top: var(--space-md);
    }

    .theme-toggle {
        margin-left: var(--space-sm);
        width: 36px;
        height: 36px;
    }
}

@media (max-width: 480px) {
    .hero-stats {
        flex-direction: column;
        gap: var(--space-md);
    }

    .card {
        margin-bottom: var(--space-lg);
    }

    .section-title {
        font-size: 1.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .social-icons a {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .theme-toggle,
    .btn,
    footer {
        display: none !important;
    }

    .hero-section {
        background: none !important;
        color: black !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-light: #000000;
        --border-medium: #000000;
        --text-secondary: #000000;
    }

    [data-theme="dark"] {
        --border-light: #ffffff;
        --border-medium: #ffffff;
        --text-secondary: #ffffff;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Additional Modern Features */

/* Navbar scroll effect */
.navbar.scrolled {
    background-color: var(--bg-card);
    box-shadow: var(--shadow-lg);
}

/* Toast notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--space-md) var(--space-lg);
    border-radius: var(--radius-md);
    color: var(--text-inverse);
    font-weight: 600;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-lg);
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-success {
    background-color: var(--accent-color);
}

.toast-error {
    background-color: #ef4444;
}

.toast-info {
    background-color: var(--primary-color);
}

/* Animation classes */
.animate-in {
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Lazy loading images */
img.lazy {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

img.lazy.loaded {
    opacity: 1;
}

/* Enhanced dropdown */
.dropdown-menu {
    background-color: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: var(--space-sm);
    margin-top: var(--space-sm);
    box-shadow: var(--shadow-xl);
}

.dropdown-item {
    border-radius: var(--radius-md);
    padding: var(--space-sm) var(--space-md);
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--primary-color);
}

.dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Enhanced navbar toggler */
.navbar-toggler {
    border: none;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    transition: all var(--transition-fast);
}

.navbar-toggler:hover {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Badge styling */
.badge {
    font-weight: 600;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

/* Enhanced focus states */
*:focus {
    outline: none;
}

.btn:focus,
.form-control:focus,
.theme-toggle:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

/* Skeleton loading */
.skeleton {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
