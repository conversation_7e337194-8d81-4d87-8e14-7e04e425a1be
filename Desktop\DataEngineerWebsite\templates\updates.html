{% extends "base.html" %}
{% block content %}
    <div class="container">
        <h1 class="mb-4">Latest Updates</h1>
        <div class="updates-section">
            <h2 class="updates-header">Platform Updates</h2>
            
            {% for update in updates %}
            <div class="update-item">
                <p class="update-date">{{ update.date_posted.strftime('%B %d, %Y') }} {% if update.category %}({{ update.category.title() }}){% endif %}</p>
                <h3 class="update-title">{{ update.title }}</h3>
                <div class="update-content">
                    {{ update.content }}
                </div>
            </div>
            {% endfor %}
            
            {% if not updates %}
            <div class="alert alert-info">
                No updates yet! Check back later for platform news and announcements.
            </div>
            {% endif %}
        </div>
    </div>
{% endblock %}
