{% extends "base.html" %}

{% block title %}Updates - DataEngineer Hub{% endblock %}

{% block content %}
<div class="container">
    <!-- Updates Header -->
    <div class="text-center mb-5">
        <h1 class="section-title">
            <i class="fas fa-bell me-2"></i>Platform Updates
        </h1>
        <p class="lead text-muted">Stay informed about the latest features, improvements, and announcements</p>
    </div>

    {% if updates %}
    <div class="updates-section">
        <div class="row g-4">
            {% for update in updates %}
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="update-date">
                                <i class="fas fa-calendar me-1"></i>
                                {{ update.date_posted.strftime('%B %d, %Y') }}
                            </div>
                            {% if update.category %}
                            <span class="tag">{{ update.category.title() }}</span>
                            {% endif %}
                        </div>

                        <h3 class="update-title">{{ update.title }}</h3>

                        <div class="update-content">
                            {{ update.content }}
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-share me-1"></i>Share
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="text-center mt-5">
            <a href="{{ url_for('new_update') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add New Update
            </a>
        </div>
    </div>
    {% else %}
    <div class="text-center py-5">
        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
        <h4 class="text-muted">No Updates Available</h4>
        <p class="text-muted">Be the first to share platform updates and announcements with the community.</p>
        <a href="{{ url_for('new_update') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create First Update
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}
