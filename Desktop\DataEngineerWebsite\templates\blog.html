{% extends "base.html" %}
{% block content %}
    <div class="container">
        <h1 class="mb-4">Blog</h1>
        <div class="row">
            <div class="col-md-8">
                {% for post in posts %}
                <div class="blog-post">
                    <h2 class="blog-post-title">{{ post.title }}</h2>
                    <p class="blog-post-meta">{{ post.date_posted.strftime('%B %d, %Y') }} by {{ post.author }}</p>
                    {% if post.image %}
                        <img src="{{ url_for('static', filename='uploads/' ~ post.image) }}" alt="{{ post.title }}" class="img-fluid mb-3">
                    {% endif %}
                    <div class="blog-post-content">
                        {{ post.content[:200] }}...
                        <a href="#" class="text-decoration-none">Read more &raquo;</a>
                    </div>
                    {% if post.tags %}
                    <div class="mt-3">
                        {% for tag in post.tags.split(',') %}
                            <span class="tag">{{ tag.strip() }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <div class="col-md-4">
                <div class="blog-sidebar">
                    <div class="blog-sidebar-section">
                        <h4 class="blog-sidebar-title">About</h4>
                        <p>Sharing insights and updates about the world of data engineering...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
