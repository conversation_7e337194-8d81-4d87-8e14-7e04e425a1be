{% extends "base.html" %}

{% block title %}Blog - DataEngineer Hub{% endblock %}

{% block content %}
<div class="container">
    <!-- Blog Header -->
    <div class="text-center mb-5">
        <h1 class="section-title">
            <i class="fas fa-blog me-2"></i>Engineering Insights
        </h1>
        <p class="lead text-muted">Discover the latest trends, tutorials, and best practices in data engineering</p>
    </div>

    <div class="row g-4">
        <!-- Main Content -->
        <div class="col-lg-8">
            {% if posts %}
                {% for post in posts %}
                <article class="card mb-4">
                    {% if post.image %}
                    <div class="card-img-top" style="height: 250px; background: url('{{ url_for('static', filename='uploads/' ~ post.image) }}') center/cover; border-radius: var(--radius-xl) var(--radius-xl) 0 0;"></div>
                    {% endif %}

                    <div class="card-body">
                        <h2 class="card-title">{{ post.title }}</h2>

                        <div class="card-meta mb-3">
                            <i class="fas fa-user me-1"></i>{{ post.author }}
                            <span class="mx-2">•</span>
                            <i class="fas fa-calendar me-1"></i>{{ post.date_posted.strftime('%B %d, %Y') }}
                            <span class="mx-2">•</span>
                            <i class="fas fa-clock me-1"></i>{{ (post.content|length / 200)|round|int }} min read
                        </div>

                        <div class="card-text">
                            {{ post.content[:300] }}{% if post.content|length > 300 %}...{% endif %}
                        </div>

                        {% if post.tags %}
                        <div class="mt-3 mb-3">
                            {% for tag in post.tags.split(',') %}
                            <span class="tag">{{ tag.strip() }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}

                        <a href="#" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-right me-2"></i>Read Full Article
                        </a>
                    </div>
                </article>
                {% endfor %}

                <!-- Pagination placeholder -->
                <nav aria-label="Blog pagination" class="mt-5">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <span class="page-link">Previous</span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link">Next</span>
                        </li>
                    </ul>
                </nav>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Blog Posts Yet</h4>
                    <p class="text-muted">Be the first to share your insights with the community.</p>
                    <a href="{{ url_for('new_blog_post') }}" class="btn btn-primary">
                        <i class="fas fa-pen me-2"></i>Write First Post
                    </a>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 100px;">
                <!-- Search -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-search me-2"></i>Search Posts
                        </h5>
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Search articles..." data-search=".card">
                            <button class="btn btn-outline-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Categories -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-folder me-2"></i>Categories
                        </h5>
                        <ul class="list-unstyled">
                            <li><a href="#" class="text-decoration-none">Data Pipeline <span class="badge bg-primary ms-2">5</span></a></li>
                            <li><a href="#" class="text-decoration-none">Machine Learning <span class="badge bg-primary ms-2">3</span></a></li>
                            <li><a href="#" class="text-decoration-none">Cloud Computing <span class="badge bg-primary ms-2">7</span></a></li>
                            <li><a href="#" class="text-decoration-none">Best Practices <span class="badge bg-primary ms-2">4</span></a></li>
                        </ul>
                    </div>
                </div>

                <!-- Recent Posts -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-clock me-2"></i>Recent Posts
                        </h5>
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-primary rounded" style="width: 50px; height: 50px;"></div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Building Scalable Data Pipelines</h6>
                                <small class="text-muted">2 days ago</small>
                            </div>
                        </div>
                        <div class="d-flex mb-3">
                            <div class="flex-shrink-0">
                                <div class="bg-accent rounded" style="width: 50px; height: 50px;"></div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-1">Apache Kafka Best Practices</h6>
                                <small class="text-muted">5 days ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
