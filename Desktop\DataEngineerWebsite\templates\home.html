{% extends "base.html" %}
{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <h1>Data Engineer Hub</h1>
        <p>Your resource for data engineering knowledge, courses, and professional development</p>
        <a href="#courses" class="btn btn-light">Browse Courses</a>
    </div>
</div>

<div class="container">
    <!-- Latest Updates Section -->
    <div class="updates-section mb-5">
        <h2 class="updates-header">Latest Updates</h2>
        <div class="row">
            {% for update in updates %}
            <div class="col-md-4">
                <div class="update-item">
                    <p class="update-date">{{ update.date_posted.strftime('%B %d, %Y') }}</p>
                    <h3 class="update-title">{{ update.title }}</h3>
                    <p class="update-content">{{ update.content[:100] }}...</p>
                </div>
            </div>
            {% else %}
            <div class="col-12">
                <p>No recent updates available.</p>
            </div>
            {% endfor %}
        </div>
        <div class="text-end mt-3">
            <a href="{{ url_for('updates') }}" class="btn btn-outline-primary">View All Updates</a>
        </div>
    </div>

    <!-- Courses Section -->
    <section id="courses" class="mb-5">
        <h2 class="mb-4">Featured Courses</h2>
        <div class="row">
            {% for course in courses %}
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title">{{ course.title }}</h5>
                        <p class="card-text">{{ course.content }}</p>
                        <video width="100%" controls>
                            <source src="{{ url_for('static', filename='uploads/' ~ course.video) }}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-12">
                <p>No courses available yet.</p>
            </div>
            {% endfor %}
        </div>
        <div class="text-end">
            <a href="{{ url_for('post_course') }}" class="btn btn-primary">Add New Course</a>
        </div>
    </section>

    <!-- Recent Blog Posts -->
    <section class="mb-5">
        <h2 class="mb-4">Recent Blog Posts</h2>
        <div class="row">
            {% for post in posts %}
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-body">
                        <h3 class="card-title">{{ post.title }}</h3>
                        <p class="text-muted">{{ post.date_posted.strftime('%B %d, %Y') }} by {{ post.author }}</p>
                        <p class="card-text">{{ post.content[:150] }}...</p>
                        <a href="{{ url_for('blog') }}" class="btn btn-outline-primary">Read more</a>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="col-12">
                <p>No blog posts available yet.</p>
            </div>
            {% endfor %}
        </div>
        <div class="text-end">
            <a href="{{ url_for('blog') }}" class="btn btn-outline-primary">View All Posts</a>
        </div>
    </section>
</div>
{% endblock %}
