from flask import Flask, render_template, url_for, redirect
from flask_sqlalchemy import SQLAlchemy
from flask_wtf import FlaskForm
from wtforms import StringField, FileField, SubmitField, TextAreaField, SelectField
from wtforms.validators import DataRequired, Length
from werkzeug.utils import secure_filename
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///site.db'
app.config['UPLOAD_FOLDER'] = os.path.join('static', 'uploads')

db = SQLAlchemy(app)

class Course(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    video = db.Column(db.String(100), nullable=False)
    date_posted = db.Column(db.DateTime, default=db.func.current_timestamp())
    
class BlogPost(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    author = db.Column(db.String(50), nullable=False, default='Admin')
    date_posted = db.Column(db.DateTime, default=db.func.current_timestamp())
    image = db.Column(db.String(100), nullable=True)
    tags = db.Column(db.String(200), nullable=True)
    
class Update(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    date_posted = db.Column(db.DateTime, default=db.func.current_timestamp())
    category = db.Column(db.String(50), nullable=True)

class CourseForm(FlaskForm):
    title = StringField('Title', validators=[DataRequired(), Length(min=2, max=100)])
    content = TextAreaField('Description', validators=[DataRequired()])
    video = FileField('Video', validators=[DataRequired()])
    submit = SubmitField('Post Course')

class BlogPostForm(FlaskForm):
    title = StringField('Title', validators=[DataRequired(), Length(min=2, max=100)])
    content = TextAreaField('Content', validators=[DataRequired()])
    author = StringField('Author', default='Admin')
    image = FileField('Featured Image')
    tags = StringField('Tags (comma-separated)')
    submit = SubmitField('Publish Post')

class UpdateForm(FlaskForm):
    title = StringField('Title', validators=[DataRequired(), Length(min=2, max=100)])
    content = TextAreaField('Content', validators=[DataRequired()])
    category = SelectField('Category', choices=[
        ('feature', 'New Feature'), 
        ('update', 'Platform Update'),
        ('event', 'Event Announcement'),
        ('news', 'Industry News')
    ])
    submit = SubmitField('Post Update')

@app.route('/')
def home():
    courses = Course.query.order_by(Course.date_posted.desc()).all()
    recent_updates = Update.query.order_by(Update.date_posted.desc()).limit(3).all()
    recent_posts = BlogPost.query.order_by(BlogPost.date_posted.desc()).limit(2).all()
    return render_template('home.html', courses=courses, updates=recent_updates, posts=recent_posts)

@app.route('/post', methods=['GET', 'POST'])
def post_course():
    form = CourseForm()
    if form.validate_on_submit():
        filename = secure_filename(form.video.data.filename)
        form.video.data.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        course = Course(title=form.title.data, content=form.content.data, video=filename)
        db.session.add(course)
        db.session.commit()
        return redirect(url_for('home'))
    return render_template('post.html', form=form)

@app.route('/blog', methods=['GET'])
def blog():
    posts = BlogPost.query.order_by(BlogPost.date_posted.desc()).all()
    return render_template('blog.html', posts=posts)

@app.route('/blog/new', methods=['GET', 'POST'])
def new_blog_post():
    form = BlogPostForm()
    if form.validate_on_submit():
        filename = None
        if form.image.data:
            filename = secure_filename(form.image.data.filename)
            form.image.data.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        post = BlogPost(title=form.title.data, content=form.content.data, author=form.author.data, image=filename, tags=form.tags.data)
        db.session.add(post)
        db.session.commit()
        return redirect(url_for('blog'))
    return render_template('new_blog_post.html', form=form)

@app.route('/updates', methods=['GET'])
def updates():
    updates = Update.query.order_by(Update.date_posted.desc()).all()
    return render_template('updates.html', updates=updates)

@app.route('/updates/new', methods=['GET', 'POST'])
def new_update():
    form = UpdateForm()
    if form.validate_on_submit():
        update = Update(title=form.title.data, content=form.content.data, category=form.category.data)
        db.session.add(update)
        db.session.commit()
        return redirect(url_for('updates'))
    return render_template('new_update.html', form=form)

if __name__ == '__main__':
    app.run(debug=True)
