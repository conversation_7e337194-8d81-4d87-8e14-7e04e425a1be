# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
ENV/
env/
.env
.venv/
dataengineer-website/
*-venv/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Database
*.db
*.sqlite
*.sqlite3
instance/

# Logs
logs/
*.log

# Local environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Uploads
static/uploads/*
!static/uploads/.gitkeep

# User-specific files
.DS_Store
Thumbs.db
