#!/usr/bin/env python
"""
Deployment script for Heroku
"""
import os
import subprocess
import sys

def run_command(command):
    """Run a shell command and print output"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, text=True, capture_output=True)
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        print(e.stderr)
        return False

def check_heroku_installed():
    """Check if Heroku CLI is installed"""
    try:
        subprocess.run(["heroku", "--version"], check=True, capture_output=True, text=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def main():
    """Main deployment function"""
    print("=== DataEngineer Website Heroku Deployment ===")
    
    # Check if Heroku is installed
    if not check_heroku_installed():
        print("Error: Heroku CLI is not installed. Please install it from https://devcenter.heroku.com/articles/heroku-cli")
        sys.exit(1)
    
    # Check if Git is initialized
    if not os.path.exists(".git"):
        print("Initializing Git repository...")
        run_command("git init")
        run_command("git add .")
        run_command('git commit -m "Initial commit"')
    
    # Heroku login
    print("\nPlease log in to Heroku:")
    run_command("heroku login")
    
    # Create Heroku app
    app_name = input("\nEnter your Heroku app name (leave blank for random name): ").strip()
    if app_name:
        create_app_cmd = f"heroku create {app_name}"
    else:
        create_app_cmd = "heroku create"
    
    if not run_command(create_app_cmd):
        print("Failed to create Heroku app. Exiting.")
        sys.exit(1)
    
    # Add PostgreSQL
    print("\nAdding PostgreSQL database...")
    run_command("heroku addons:create heroku-postgresql:hobby-dev")
    
    # Set config variables
    print("\nSetting up environment variables...")
    secret_key = os.urandom(24).hex()
    run_command(f"heroku config:set SECRET_KEY={secret_key}")
    
    # Deploy to Heroku
    print("\nDeploying to Heroku...")
    run_command("git push heroku master || git push heroku main")
    
    # Initialize the database
    print("\nInitializing the database...")
    run_command('heroku run "python -c \'from app import app, db; app.app_context().push(); db.create_all()\'"')
    
    # Open the app
    print("\nOpening the app...")
    run_command("heroku open")
    
    print("\n=== Deployment Complete ===")
    print("Your DataEngineer Website is now deployed to Heroku!")

if __name__ == "__main__":
    main()
