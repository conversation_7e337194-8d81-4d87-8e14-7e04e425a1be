#!/usr/bin/env python
"""
Deployment script helper for PythonAnywhere
"""
import os
import sys

def main():
    """Generate deployment instructions for PythonAnywhere"""
    print("=== DataEngineer Website PythonAnywhere Deployment Guide ===")
    
    # Get current directory
    current_dir = os.path.abspath(os.path.dirname(__file__))
    project_name = os.path.basename(current_dir)
    
    print(f"\nThis script will guide you through deploying {project_name} to PythonAnywhere.")
    print("\nFollow these steps:")
    
    print("\n1. Sign up for a free PythonAnywhere account at https://www.pythonanywhere.com")
    
    print("\n2. Upload your files to PythonAnywhere:")
    print("   a. Log in to PythonAnywhere")
    print("   b. Go to the 'Files' tab")
    print("   c. Create a new directory for your app (e.g., 'dataengineer-website')")
    print("   d. Upload all the files from this directory to PythonAnywhere")
    print("   e. Alternatively, if your code is on GitHub, you can clone the repository using:")
    print("      $ git clone https://github.com/yourusername/dataengineer-website.git")
    
    print("\n3. Set up a virtual environment:")
    print("   a. Go to the 'Consoles' tab")
    print("   b. Start a new Bash console")
    print("   c. Run the following commands:")
    print("      $ cd dataengineer-website")
    print("      $ python -m venv venv")
    print("      $ source venv/bin/activate")
    print("      $ pip install -r requirements.txt")
    
    print("\n4. Set up the web app:")
    print("   a. Go to the 'Web' tab")
    print("   b. Click 'Add a new web app'")
    print("   c. Choose 'Manual configuration'")
    print("   d. Select Python version (3.11)")
    
    print("\n5. Configure the WSGI file:")
    print("   a. Click on the WSGI configuration file link")
    print("   b. Replace the content with the following:")
    
    wsgi_content = f"""import sys
import os

# Add your project directory to the sys.path
path = '/home/<USER>/dataengineer-website'
if path not in sys.path:
    sys.path.insert(0, path)

# Set environment variables
os.environ['SECRET_KEY'] = 'your-secret-key'

# Import your Flask app
from app import app as application
"""
    
    print(f"\n```python\n{wsgi_content}\n```")
    
    print("\n6. Configure the virtual environment:")
    print("   a. In the 'Web' tab, under 'Virtualenv', enter:")
    print("      /home/<USER>/dataengineer-website/venv")
    
    print("\n7. Set up the static files:")
    print("   a. In the 'Web' tab, add the following static directory mappings:")
    print("      URL: /static/")
    print("      Directory: /home/<USER>/dataengineer-website/static")
    
    print("\n8. Initialize the database:")
    print("   a. Go back to your Bash console")
    print("   b. Run:")
    print("      $ python")
    print("      >>> from app import app, db")
    print("      >>> with app.app_context():")
    print("      ...     db.create_all()")
    print("      >>> exit()")
    
    print("\n9. Reload your web app:")
    print("   a. Go back to the 'Web' tab")
    print("   b. Click the 'Reload' button")
    
    print("\n10. Your app should now be live at:")
    print("    https://yourusername.pythonanywhere.com")
    
    print("\n=== Deployment Guide Complete ===")
    print("Follow the steps above to deploy your DataEngineer Website to PythonAnywhere.")

if __name__ == "__main__":
    main()
