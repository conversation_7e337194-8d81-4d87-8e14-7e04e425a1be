# Data Engineer Website

A modern website for Data Engineers to upload and share courses and educational videos.

## Features

- User-friendly interface for showcasing courses
- Video upload and playback capabilities
- Responsive design for all devices
- Modern UI with Bootstrap and custom CSS
- Backend powered by Flask

## Technologies Used

- **Frontend**: HTML, CSS, JavaScript, Bootstrap 5
- **Backend**: Python, Flask
- **Database**: SQLite (development), PostgreSQL (production)
- **Deployment**: Heroku

## Local Setup

### Prerequisites

- Python 3.11+
- UV package manager

### Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/dataengineer-website.git
cd dataengineer-website
```

2. Install dependencies using UV:
```
uv sync
```

3. Initialize the database:
```python
python
>>> from app import app, db
>>> with app.app_context():
...     db.create_all()
>>> exit()
```

4. Run the application:
```
python app.py
```

5. Access the website at `http://localhost:5000`

## Deployment to Heroku

### Prerequisites

- Heroku CLI
- Git

### Steps

1. Login to <PERSON>ku:
```
heroku login
```

2. Create a new Heroku app:
```
heroku create dataengineer-website
```

3. Add a PostgreSQL database:
```
heroku addons:create heroku-postgresql:hobby-dev
```

4. Configure environment variables:
```
heroku config:set SECRET_KEY=your_secret_key
```

5. Deploy to Heroku:
```
git push heroku main
```

6. Initialize the database:
```
heroku run python
>>> from app import app, db
>>> with app.app_context():
...     db.create_all()
>>> exit()
```

7. Open the app:
```
heroku open
```

## Alternative Deployment Options

### Using PythonAnywhere

1. Sign up for a PythonAnywhere account at [pythonanywhere.com](https://www.pythonanywhere.com)
2. Upload your code to PythonAnywhere
3. Set up a web app using Flask
4. Configure your virtualenv and install the requirements
5. Set up your WSGI file to point to your Flask app

### Using DigitalOcean App Platform

1. Sign up for DigitalOcean and create a new App
2. Connect your GitHub repository
3. Configure your app to use Python and Gunicorn
4. Deploy your app

## Project Structure

```
dataengineer-website/
├── app.py                # Main Flask application
├── templates/            # HTML templates
│   ├── base.html         # Base template with navbar and footer
│   ├── home.html         # Homepage showing all courses
│   └── post.html         # Form for adding new courses
├── static/               # Static files
│   ├── css/              # CSS stylesheets
│   │   └── style.css     # Custom CSS
│   ├── js/               # JavaScript files
│   │   └── main.js       # Custom JS
│   ├── img/              # Images
│   └── uploads/          # Uploaded videos
├── requirements.txt      # Python dependencies
├── Procfile             # Heroku configuration
└── runtime.txt          # Python version for Heroku
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -am 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
