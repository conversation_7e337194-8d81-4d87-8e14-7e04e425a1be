{% extends "base.html" %}

{% block title %}Write New Post - DataEngineer Hub{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="section-title">
                    <i class="fas fa-pen me-2"></i>Write New Post
                </h1>
                <p class="lead text-muted">Share your insights and experiences with the data engineering community</p>
            </div>

            <!-- Form Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Post Details
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                        {{ form.hidden_tag() }}

                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="title" class="form-label">
                                        <i class="fas fa-heading me-2"></i>Post Title
                                    </label>
                                    {{ form.title(class="form-control", placeholder="Enter a compelling title", required=true) }}
                                    <div class="invalid-feedback">
                                        Please provide a post title.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="author" class="form-label">
                                        <i class="fas fa-user me-2"></i>Author
                                    </label>
                                    {{ form.author(class="form-control", placeholder="Your name") }}
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="content" class="form-label">
                                <i class="fas fa-align-left me-2"></i>Content
                            </label>
                            {{ form.content(class="form-control", rows="12", placeholder="Write your blog post content here. Share your knowledge, experiences, and insights...", required=true) }}
                            <div class="form-text">Use markdown formatting for better presentation.</div>
                            <div class="invalid-feedback">
                                Please provide post content.
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="image" class="form-label">
                                        <i class="fas fa-image me-2"></i>Featured Image
                                    </label>
                                    {{ form.image(class="form-control", accept="image/*") }}
                                    <div class="form-text">Upload a featured image for your post (optional).</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tags" class="form-label">
                                        <i class="fas fa-tags me-2"></i>Tags
                                    </label>
                                    {{ form.tags(class="form-control", placeholder="data engineering, python, kafka") }}
                                    <div class="form-text">Separate tags with commas.</div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-3 justify-content-end">
                            <a href="{{ url_for('blog') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="button" class="btn btn-outline-primary">
                                <i class="fas fa-save me-2"></i>Save Draft
                            </button>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>

            <!-- Writing Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Writing Tips
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Content Guidelines</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Start with a compelling introduction</li>
                                <li><i class="fas fa-check text-success me-2"></i>Use clear headings and subheadings</li>
                                <li><i class="fas fa-check text-success me-2"></i>Include practical examples</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Best Practices</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Keep paragraphs concise</li>
                                <li><i class="fas fa-check text-success me-2"></i>Add relevant tags for discoverability</li>
                                <li><i class="fas fa-check text-success me-2"></i>Proofread before publishing</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
