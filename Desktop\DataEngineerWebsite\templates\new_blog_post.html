{% extends "base.html" %}
{% block content %}
<div class="container">
    <h1 class="mb-4">Create New Blog Post</h1>
    <div class="card">
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}
                <div class="mb-3">
                    <label for="title" class="form-label">Title</label>
                    {{ form.title(class="form-control", placeholder="Blog Post Title") }}
                </div>
                <div class="mb-3">
                    <label for="author" class="form-label">Author</label>
                    {{ form.author(class="form-control", placeholder="Your Name") }}
                </div>
                <div class="mb-3">
                    <label for="content" class="form-label">Content</label>
                    {{ form.content(class="form-control", rows="10", placeholder="Write your blog post here...") }}
                </div>
                <div class="mb-3">
                    <label for="image" class="form-label">Featured Image</label>
                    {{ form.image(class="form-control") }}
                </div>
                <div class="mb-3">
                    <label for="tags" class="form-label">Tags (comma-separated)</label>
                    {{ form.tags(class="form-control", placeholder="data, engineering, big data") }}
                </div>
                <div class="mb-3">
                    {{ form.submit(class="btn btn-primary") }}
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
